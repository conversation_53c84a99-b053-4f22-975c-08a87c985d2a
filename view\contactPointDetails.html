<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>立法联系点</title>
  <link rel="stylesheet" href="../css/common.css">
  <link rel="stylesheet" href="../css/vant.css">
  <style>
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      height: 100%;
    }


    .details_top {
      position: relative;
      z-index: 1;
    }

    .details_top_img {
      width: 100%;
      height: 220px;
    }

    .details_follow {
      position: absolute;
      top: 10px;
      right: 12px;
      background: rgba(0, 0, 0, 0.2);
      padding: 4px 10px;
      border-radius: 15px;
    }

    .details_follow_text {
      color: #fff;
      font-size: 14px;
    }

    .details_content {
      margin-top: -45px;
    }

    /* 联系点详情 */
    .details_information {
      background: #FFFFFF;
      border-radius: 8px;
      margin: 12px;
      padding: 15px;
      z-index: 2;
      position: relative;
    }

    .details_triangle {
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      margin-right: 5px;
    }

    .details_title {
      font-size: 18px;
      color: #333333;
      font-weight: 600;
    }

    .details_user {
      margin-top: 10px;
    }

    .icon_box {
      width: 15px;
      height: 15px;
      margin-right: 5px;
    }

    .text_box {
      font-size: 15px;
      color: #333333;
    }

    .details_tag_box {
      background: rgba(246, 147, 28, 0.08);
      padding: 3px 10px;
    }

    .details_tag {
      font-size: 14px;
      color: #F6931C;
    }

    .details_address {
      margin-top: 10px;
      font-size: 14px;
      color: #333333;
    }

    .details_introduction {
      margin-top: 10px;
      font-size: 14px;
      color: #999999;
    }

    /* 留言按钮 */
    .message_button {
      background: linear-gradient(90deg, #0D75FF 0%, #41BBFF 100%);
      color: #fff;
      border: none;
      border-radius: 20px;
      width: 100%;
      height: 40px;
      font-size: 16px;
      margin-top: 8px;
    }

    .message_icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }

    /* 相关单位 */
    .relevant_units {
      margin: 12px;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
    }

    .toggle-btn {
      padding: 0;
      background-color: transparent;
    }

    .toggle-btn:active {
      background-color: transparent;
    }

    .toggle-text {
      font-size: 14px;
      color: #999999;
      margin-right: 3px;
    }

    .toggle-arrow {
      font-size: 12px;
      color: #999999;
      font-weight: normal;
    }

    .relevant-unit-list {
      width: 100%;
      margin-top: 16px;
    }

    .unit-item {
      background-color: #f8f8f8;
      border-radius: 6px;
      padding: 10px 12px;
      margin-bottom: 12px;
      transition: all 0.3s ease;
    }

    .unit-item:last-child {
      margin-bottom: 0;
    }

    .unit-item:active {
      background-color: #f0f0f0;
    }

    .unit-name {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 6px;
    }

    .unit-address {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      word-break: break-all;
    }

    /* 信息发布 */
    .information_release {
      margin: 12px;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
    }

    .more-btn {
      color: #999999;
      font-size: 14px;
    }

    .arrow-right {
      width: 20px;
      height: 20px;
    }

    .release-list {
      width: 100%;
    }

    .release-item {
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .release-item:last-child {
      border-bottom: none;
    }

    .release-title {
      display: block;
      font-size: 16px;
      color: #333333;
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .release-date {
      display: block;
      font-size: 13px;
      color: #999999;
    }

    /* 联系点活动 */
    .contact_point_activity {
      margin: 12px;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
    }

    .activity-list {
      width: 100%;
      margin-top: 12px;
    }

    .activity-item {
      background-color: #F0F4FF;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 12px;
    }

    .activity-item:last-child {
      margin-bottom: 0;
    }

    .activity-status {
      padding: 3px 10px;
      background: rgba(246, 147, 28, 0.08);
      font-size: 14px;
      color: #F6931C;
      border-radius: 3px;
    }

    .activity-status-no {
      background: rgba(102, 102, 102, 0.1);
      padding: 4px 10px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: rgb(102, 102, 102);
    }

    .activity-title {
      display: block;
      font-size: 15px;
      color: #333333;
      margin-top: 12px;
      margin-bottom: 10px;
      font-weight: 500;
      line-height: 1.5;
    }

    .activity-location,
    .activity-time {
      display: block;
      font-size: 13px;
      color: #333;
      margin-bottom: 13px;
      line-height: 1.4;
    }

    .activity-time {
      font-size: 12px;
    }

    .activity-detail {
      text-align: center;
      margin-top: 10px;
      font-size: 14px;
      color: #104B8B;
      font-weight: 500;
    }

    /* 联系点活动 */
    .legislative_consultation {
      margin: 12px;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
    }

    .consultation-list {
      margin-top: 10px;
    }

    .consultation-item {
      padding: 12px 0;
    }

    .consultation-top {
      margin-bottom: 10px;
    }

    .consultation-status {
      padding: 3px 10px;
      background-color: #FFF5E6;
      color: #E67E22;
      font-size: 14px;
      border-radius: 3px;
    }

    .consultation-type {
      color: #165DFF;
      font-size: 14px;
    }

    .consultation-title {
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      margin-bottom: 10px;
      word-break: break-all;
    }

    .consultation-date {
      font-size: 13px;
      color: #666666;
      line-height: 20px;
    }

    .consultation-divider {
      margin-top: 12px;
      height: 1px;
      background-color: #EEEEEE;
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
      font-size: 14px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 顶部背景图、关注 -->
    <div class="details_top">
      <img :src="info.coverDrawingId?fileImgUrl+info.coverDrawingId:'../img/legislation_lxd.png'" mode=""
        class="details_top_img" />
      <div class="details_follow" v-if="false">
        <span class="details_follow_text">+关注</span>
      </div>
    </div>
    <div class="details_content">
      <!-- 联系点详情 -->
      <div class="details_information" v-cloak>
        <div class="flex_box flex_align_center">
          <span class="details_triangle" style="border-left: 6px solid rgba(16, 75, 139);"></span>
          <span class="details_triangle" style="border-left: 6px solid rgba(16, 75, 139, 0.5);"></span>
          <span class="details_title">{{info.contactPointName}}</span>
        </div>
        <div class="flex_box flex_align_center flex_justify_between details_user">
          <div class="flex_box flex_align_center" v-if="info.contactPerson || info.contactMobile">
            <img src="../img/icon_user1.png" mode="" class="icon_box" />
            <span class="text_box">{{info.contactPerson}}-{{info.contactMobile}}</span>
          </div>
          <div class="details_tag_box">
            <span class="details_tag">{{info.contactPointLevel?item.contactPointLevel.label:'暂无'}}</span>
          </div>
        </div>
        <div class="details_address">{{info.contactPointAddress||'暂无详情地址'}}</div>
        <div class="details_introduction">{{info.contactPointIntroduction||'暂无详情介绍'}}</div>
        <button class="message_button flex_box flex_align_center flex_justify_center" @click="onMessageTap">
          <img src="../img/icon_msg.png" alt="留言" class="message_icon" />
          留言
        </button>
      </div>
      <!-- 相关单位 -->
      <div class="relevant_units">
        <div class="flex_box flex_align_center flex_justify_between">
          <div class="flex_box flex_align_center">
            <div class="section-line"></div>
            <span class="section-title">相关单位</span>
          </div>
          <div class="toggle-btn flex_box flex_align_center" @click="toggleUnitsExpand">
            <span class="toggle-text" v-cloak>{{isUnitsExpanded ? '收起' : '展开'}}</span>
            <span class="toggle-arrow" v-cloak>{{isUnitsExpanded ? '▲' : '▼'}}</span>
          </div>
        </div>
        <div class="relevant-unit-list" v-if="isUnitsExpanded">
          <template v-if="relevantUnitList&&relevantUnitList.length>0">
            <div v-for="item in relevantUnitList" :key="item.id" class="unit-item">
              <div class="unit-name">{{item.contactUnitName}}</div>
              <div class="unit-address">{{item.contactUnitAddress?item.contactUnitAddress:'暂无详细地址'}}</div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </div>
      </div>
      <!-- 信息发布 -->
      <div class="information_release">
        <div class="flex_box flex_align_center flex_justify_between">
          <div class="flex_box flex_align_center">
            <div class="section-line"></div>
            <span class="section-title">信息发布</span>
          </div>
          <div class="more-btn flex_box flex_align_center" @click="openInformationReleaseMore">
            <span>更多</span>
            <img src="../img/arrow_right.png" mode="" class="arrow-right" />
          </div>
        </div>
        <div class="release-list" v-cloak>
          <template v-if="informationReleaseList&&informationReleaseList.length>0">
            <div v-for="item in informationReleaseList" :key="item.id" class="release-item"
              @click="openInformationReleaseDetails(item)">
              <span class="release-title">{{item.title}}</span>
              <span class="release-date">{{formatTime(item.publishTime)}}</span>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </div>
      </div>
      <!-- 联系点活动 -->
      <div class="contact_point_activity">
        <div class="flex_box flex_align_center flex_justify_between">
          <div class="flex_box flex_align_center">
            <div class="section-line"></div>
            <span class="section-title">联系点活动</span>
          </div>
          <div class="more-btn flex_box flex_align_center" @click="openActivityMore">
            <span>更多</span>
            <image src="../img/arrow_right.png" mode="" class="arrow-right" />
          </div>
        </div>
        <div class="activity-list" v-cloak>
          <template v-if="activityList&&activityList.length>0">
            <div v-for="item in activityList" :key="item.id" class="activity-item" @click="openActivityDetails(item)">
              <div class="flex_box">
                <div :class="item.status=='进行中'||item.status=='未开始'?'activity-status':'activity-status-no'">
                  {{item.status}}</div>
                <div></div>
              </div>
              <span class="activity-title">{{item.title}}</span>
              <span class="activity-location">活动地点：{{item.publishLocation||'暂无'}}</span>
              <span class="activity-time">活动时间：{{formatTime(item.startDate)}} 至 {{formatTime(item.endDate)}}</span>
              <div class="activity-detail">查看详情</div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </div>
      </div>
      <!-- 立法征询 -->
      <div class="legislative_consultation">
        <div class="flex_box flex_align_center flex_justify_between">
          <div class="flex_box flex_align_center">
            <div class="section-line"></div>
            <span class="section-title">立法征询</span>
          </div>
          <div class="more-btn flex_box flex_align_center" @click="openLegislativeMore">
            <span>更多</span>
            <img src="../img/arrow_right.png" mode="" class="arrow-right" />
          </div>
        </div>
        <div class="consultation-list" v-cloak>
          <template v-if="legislativeConsultationList&&legislativeConsultationList.length>0">
            <div v-for="(item,index) in legislativeConsultationList" :key="item.id" class="consultation-item"
              @click="openLegislativeDetails(item)">
              <div class="consultation-top flex_box flex_align_center flex_justify_between">
                <div class="consultation-status">{{item.joinState}}</div>
                <div class="consultation-type">{{item.opinionSource}}</div>
              </div>
              <div class="consultation-title two_text">{{item.title}}</div>
              <div class="consultation-date">征集日期：{{formatTime(item.startTime)}} 至 {{formatTime(item.endTime)}}</div>
              <div class="consultation-divider" v-if="index < legislativeConsultationList.length - 1"></div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicLawContactPointInfoKey = '04c288306a2f76656e6c8aaed3a5086ebe7bc2b127ec0b34b792941793c30c413fca0a68e748123370329b777f65e73c55fbee2d16fae35d650d76dda5e55d9a5f' // 立法联系点详情公钥
    var publicContactUnitListKey = '040da38f25a0a250ad44f626655ebaee584f8495eaa7578f4bf49e54448d03172312036807af7d05407f3d96e5be9133a1cc8c66e9e503aa76730476cb68007ebd' // 立法联系点相关单位列表公钥
    var publicReleaseListKey = '0498561f9e1d00d373fb262dcddb5040276d2d335677431330ebfe60314857aa3a2ab886363243dd4a994e0276b1f456e229f16d56f67567a40890172dad20f855' // 获取信息发布公钥
    var publicOpinionListKey = '04a24ffac5944585d915cb23e1a6cac255d2586d66eff6e01d4d5b2ffef94bcd365131d86f6b1c8c2c153508e7aa86410f0f7290102d45a172c383442702a3470a' // 获取立法征询公钥

    var id = ''
    var app = new Vue({
      el: "#app",
      data: {
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        info: {},
        isUnitsExpanded: false,
        relevantUnitList: [],
        informationReleaseList: [],
        activityList: [],
        legislativeConsultationList: [],
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          this.getContactPointDetail()
          this.getUnitList()
          this.getInformationReleaseList()
          this.getActivityList()
          this.getLegislativeConsultationList()
        }
      },
      methods: {
        // 获取联系点详情
        getContactPointDetail () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawContactPointInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicLawContactPointInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取联系点详情', ret)
              that.info = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getContactPointDetail()
            }
          })
        },
        // 获取相关单位列表
        getUnitList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawContactUnitList'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 99,
            query: { isUsing: 1, relationPointId: id }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicContactUnitListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取相关单位列表', ret)
              that.relevantUnitList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getUnitList()
            }
          })
        },
        // 获取信息发布列表
        getInformationReleaseList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawConsultRegulationList'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 3,
            tableId: 'law_consult_regulation_info',
            query: { consultRegulationStatus: 2, lawPointId: id, moduleType: '1' }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicReleaseListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取信息发布列表', ret)
              that.informationReleaseList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInformationReleaseList()
            }
          })
        },
        // 获取联系点活动列表
        getActivityList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawConsultRegulationList'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 3,
            tableId: 'law_consult_regulation',
            query: { consultRegulationStatus: 2, lawPointId: id, moduleType: '2' }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicReleaseListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取联系点活动列表', ret)
              that.activityList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getActivityList()
            }
          })
        },
        // 获取立法征询列表
        getLegislativeConsultationList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'legislationOpinionList'
          var interfacecontent = {
            areaId: '371500',
            isManage: false,
            opinionType: '',
            pageNo: 1,
            pageSize: 3,
            positionId: id,
            tableId: 'law_consult_regulation',
            query: { businessCode: 'legis_contact_point', isDraft: 0 }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicOpinionListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取立法征询列表', ret)
              ret.data.forEach(item => {
                item.joinState = item.hasPartIn == "0" ? "未参与" : item.hasPartIn == "1" ? "已参与" : ""
              })
              that.legislativeConsultationList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getLegislativeConsultationList()
            }
          })
        },
        // 打开留言页面
        onMessageTap () {
          window.location.href = './contactPointMessage.html?id=' + this.info.id
        },
        // 相关单位展开收起
        toggleUnitsExpand () {
          this.isUnitsExpanded = !this.isUnitsExpanded
        },
        // 信息发布进入详情
        openInformationReleaseDetails (item) {
          window.location.href = './newsDetails.html?id=' + item.id + '&type=informationRelease'
        },
        // 信息发布更多
        openInformationReleaseMore () {
          window.location.href = './newsList.html?id=' + id + '&type=informationRelease'
        },
        // 站点活动进入详情
        openActivityDetails (item) {
          window.location.href = './contactPointActivityDetails.html?id=' + item.id
        },
        // 联系点活动更多
        openActivityMore () {
          window.location.href = './contactPointActivityMore.html?id=' + id
        },
        // 立法征询进入详情
        openLegislativeDetails (item) {
          window.location.href = './contactPointLegislativeDetails.html?id=' + item.id
        },
        // 立法征询更多
        openLegislativeMore () {
          window.location.href = './contactPointLegislativeMore.html?id=' + id
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>