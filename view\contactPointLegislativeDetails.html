<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background-color: #FFFFFF;
    }

    /* 详情信息 */
    .legislative_details {
      padding: 20px;
    }

    .legislative_details_title {
      font-size: 20px;
      font-weight: 800;
      color: #333333;
      line-height: 30px;
      margin-bottom: 10px;
    }

    .legislative_details_tag {
      background: rgba(246, 147, 28, 0.08);
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
      padding: 3px 10px;
    }

    .legislative_details_status {
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
    }

    .legislative_details_item {
      color: rgb(51, 51, 51);
      font-weight: 600;
      font-size: 15px;
      padding: 6px 0;
    }

    .legislative_details_item span {
      color: rgb(51, 51, 51);
      font-weight: normal;
    }

    .legislative_details_content {
      font-size: 16px;
      color: #333333;
      line-height: 30px;
      padding: 5px 0;
    }

    /* 征集结果反馈样式 */
    .feedback_section {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
    }

    .feedback_text {
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 25px;
      margin-bottom: 10px;
    }

    .file_box {
      display: flex;
      align-items: center;
      border: 1px solid #F4F5F7;
      margin: 5px 15px;
      padding: 5px 8px;
    }

    .file_icon_pdf {
      width: 25px;
      height: 23px;
    }

    .file_word_icon {
      width: 25px;
      height: 23px;
    }

    .file_name {
      font-weight: 400;
      font-size: 15px;
      color: #666666;
      margin-left: 10px;
    }

    .feedback_info {
      font-size: 14px;
      color: #999999;
      margin-top: 10px;
    }

    /* 外部链接 */
    .external_links {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
      font-size: 16px;
      color: #148fe0;
    }

    /* 全部评论 */
    .comment_section {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
    }

    .comment_list {
      padding: 8px 0;
    }

    .comment_item {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f5f5f5;
    }

    .comment_item:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .comment_user {
      margin-bottom: 10px;
    }

    .comment_avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .comment_nickname {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }

    .comment_content {
      font-weight: 400;
      font-size: 15px;
      color: #333;
      line-height: 20px;
      margin-bottom: 10px;
    }

    .comment_time {
      font-size: 14px;
      color: #999;
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
      font-size: 14px;
    }

    /* 评论输入框样式 */
    .comment_input_section {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 10px 15px;
      border-top: 1px solid #e5e5e5;
      z-index: 100;
    }

    .comment_input_box {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .comment_input {
      flex: 1;
      height: 36px;
      border: 1px solid #e5e5e5;
      border-radius: 18px;
      padding: 0 15px;
      font-size: 14px;
      color: #333;
      background: #f8f8f8;
    }

    .comment_input::placeholder {
      color: #999;
    }

    .comment_num {
      font-size: 14px;
      color: #999;
    }

    .comment_count {
      font-size: 12px;
      color: #999;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .comment_icon {
      width: 23px;
      height: 23px;
      margin-left: 5px;
    }

    /* 评论弹窗样式 */
    .comment_popup {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      display: flex;
      align-items: flex-end;
    }

    .comment_popup_content {
      width: 100%;
      background: #fff;
      border-radius: 12px 12px 0 0;
      padding: 20px;
      animation: slideUp 0.3s ease-out;
    }

    @keyframes slideUp {
      from {
        transform: translateY(100%);
      }

      to {
        transform: translateY(0);
      }
    }

    .comment_popup_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .comment_popup_title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .comment_popup_close {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 18px;
      cursor: pointer;
    }

    .comment_textarea {
      width: 100%;
      min-height: 120px;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      color: #333;
      resize: none;
      outline: none;
      box-sizing: border-box;
    }

    .comment_textarea::placeholder {
      color: #999;
    }

    .comment_popup_footer {
      display: flex;
      gap: 15px;
      margin-top: 20px;
    }

    .comment_btn {
      flex: 1;
      height: 44px;
      border: none;
      border-radius: 22px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .comment_btn_cancel {
      background: #f5f5f5;
      color: #666;
    }

    .comment_btn_submit {
      background: #1890ff;
      color: #fff;
    }

    /* 投票样式 */
    .vote_box {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
    }

    .vote_title {
      text-align: center;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      line-height: 25px;
    }

    .vote_desc {
      margin-bottom: 20px;
      font-size: 14px;
      color: #666;
    }

    .select_item {
      padding: 10px 0;
      margin-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
    }

    .option_text {
      font-size: 15px;
      color: #333;
      line-height: 22px;
    }

    .option_count {
      font-size: 14px;
      color: #999;
      margin-left: 10px;
    }

    .option_checkbox {
      width: 18px;
      height: 18px;
      border: 1px solid #ddd;
      border-radius: 50%;
      transition: all 0.3s;
    }

    .option_checkbox.checked {
      border-color: #1890ff;
      background-color: #1890ff;
    }

    .checkbox_inner {
      width: 10px;
      height: 10px;
    }

    .checkmark {
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    .vote_button {
      width: 100%;
      height: 44px;
      background: #1890ff;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
    }

    .vote_button:hover:not(.disabled) {
      background: #40a9ff;
    }

    .vote_button.disabled {
      background: #f5f5f5;
      cursor: not-allowed;
    }

    .vote_button_text {
      font-size: 16px;
      color: white;
      font-weight: 500;
    }

    .vote_button.disabled .vote_button_text {
      color: #999;
    }

    /* 为底部评论输入框留出空间 */
    .body_box {
      padding-bottom: 60px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 详情信息 -->
    <div class="legislative_details" v-cloak>
      <div class="legislative_details_title">{{info.title}}</div>
      <div class="flex_box flex_align_center flex_justify_between" style="margin-bottom: 10px;">
        <div class="legislative_details_tag" v-if="info.legisOpinionType.value">{{info.legisOpinionType.name}}
        </div>
        <div class="legislative_details_status">{{info.enlistStatus}}</div>
      </div>
      <div class="legislative_details_item">发布机构：<span>{{info.officeName}}</span></div>
      <div class="legislative_details_item">开始时间：<span>{{formatTime(info.startTime)}}</span></div>
      <div class="legislative_details_item">结束时间：<span>{{formatTime(info.endTime)}}</span></div>
      <div class="legislative_details_content" v-html="info.opinionContent"></div>
      <div class="file_box" v-for="(item,index) in info.attachments" :key="index" @click="clickfile(item)">
        <image :src="item.extName=='pdf'?'../img/pdf.png':'../img/word3.png'" mode=""
          :class="item.extName=='pdf'?'file_icon_pdf':'file_word_icon'" />
        <div class="file_name one_text">{{item.originalFileName}}</div>
      </div>
    </div>

    <!-- 征集结果反馈 -->
    <div class="feedback_section" v-if="info.feedback">
      <div class="section-header flex_box flex_align_center" style="margin-bottom: 10px;">
        <div class="section-line"></div>
        <span class="section-title">征集结果反馈</span>
      </div>
      <div class="feedback_content" v-cloak>
        <div class="feedback_text" v-html="info.feedback.content"></div>
        <div class="file_box" style="margin: 10px 0;" v-for="(item,index) in info.feedback.attachments" :key="index"
          @click="clickfile(item)">
          <image :src="item.extName=='pdf'?'../img/pdf.png':'../img/word3.png'" mode=""
            :class="item.extName=='pdf'?'file_icon_pdf':'file_word_icon'" />
          <div class="file_name one_text">{{item.originalFileName}}</div>
        </div>
        <div class="feedback_info flex_box flex_align_center flex_justify_between">
          <div class="feedback_author">反馈人：{{info.feedback.createName}}</div>
          <div class="feedback_time">反馈时间：{{formatTime(info.feedback.createDate)}}</div>
        </div>
      </div>
    </div>

    <!-- 外部链接形式 -->
    <div v-if="info.opinionLayout.value == 3" class="external_links" @click="openOutLink" v-cloak>外部链接：{{info.outLink}}
    </div>

    <!-- 投票形式 -->
    <div class="vote_box" v-if="info.opinionLayout.value == '2'" v-cloak>
      <div class="vote_title">{{info.voteTopic.topic}}</div>
      <div class="vote_desc">{{info.voteTopic.maxVote>1?'多选':'单选'}}，最多{{info.voteTopic.maxVote}}项</div>
      <div class="select_item flex_box flex_justify_between flex_align_center"
        v-for="(item,index) in info.voteTopicOptions" :key="index" @click="selectOption(index)">
        <div class="flex_box flex_justify_between flex_align_center flex_1">
          <span class="option_text">{{item.optionContent}}</span>
          <span class="option_count" v-if="info.hasVote">{{item.count}}票</span>
        </div>
        <div class="option_checkbox flex_box flex_justify_center flex_align_center" :class="{'checked': item.choose}">
          <div class="checkbox_inner flex_box flex_justify_center flex_align_center" v-if="item.choose">
            <span class="checkmark">✓</span>
          </div>
        </div>
      </div>
      <div class="vote_button"
        :class="{'disabled': info.hasVote || info.enlistStatus == '已结束' || info.enlistStatus == '未开始'}"
        @click="submitVote">
        <span class="vote_button_text">{{info.hasVote ? '已投票' : (info.enlistStatus == '已结束' || info.enlistStatus ==
          '未开始' ? '投票'+info.enlistStatus : '投票')}}</span>
      </div>
    </div>

    <!-- 全部评论 -->
    <div class="comment_section" v-if="info.opinionLayout.value != '2'">
      <div class="section-header flex_box flex_align_center" style="margin-bottom: 10px;">
        <div class="section-line"></div>
        <span class="section-title">全部评论</span>
      </div>
      <div class="comment_list">
        <template v-if="commentList&&commentList.length>0">
          <div class="comment_item" v-for="(item,index) in commentList" :key="index">
            <div class="comment_user flex_box flex_align_center">
              <image class="comment_avatar" :src="item.headImg?fileImgUrl+item.headImg:'../img/def_head_img.jpg'"
                mode="aspectFill" />
              <div class="comment_nickname">{{item.createName}}</div>
            </div>
            <div class="comment_content">{{item.commentContent}}</div>
            <div class="comment_time">{{formatTime(item.createDate)}}</div>
          </div>
        </template>
        <template v-else>
          <div class="nodata">暂无数据</div>
        </template>
      </div>
    </div>

    <!-- 评论输入框 -->
    <div class="comment_input_section" v-if="info.opinionLayout.value != '2'">
      <div class="comment_input_box">
        <input type="text" class="comment_input" placeholder="发表评论" readonly @click="showCommentPopup" />
        <div class="comment_count">
          <span class="comment_num">{{commentList.length}}</span>
          <image class="comment_icon" src="../img/icon_common.png" mode="aspectFill" />
        </div>
      </div>
    </div>

    <!-- 评论弹窗 -->
    <div class="comment_popup" v-if="showPopup" @click="hideCommentPopup">
      <div class="comment_popup_content" @click.stop>
        <div class="comment_popup_header">
          <div class="comment_popup_title">发表评论</div>
          <div class="comment_popup_close" @click="hideCommentPopup">×</div>
        </div>
        <textarea class="comment_textarea" v-model="commentText" placeholder="请输入您的评论" maxlength="500"></textarea>
        <div class="comment_popup_footer">
          <button class="comment_btn comment_btn_cancel" @click="hideCommentPopup">取消</button>
          <button class="comment_btn comment_btn_submit" @click="submitComment">发送</button>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicLegislativeInfoKey = '046f8ec032581100b3735a9264da46bcdf4b848af4639d262e76a8653f861588e64ed245cb197ca7387d5aa4305d5dc2d3a729b6ab6689938a37449f548fe68c1d' // 立法征询详情公钥
    var publicCommentListKey = '04fdf72f327b7ab14a4b759f49f4522c7d9e723bca81780f22117dcbd5f4ad31b9c8137e9770865bdbaf41642d844ee7d5229233ad059b428185df02eed8c63b24' // 评论列表公钥
    var publicCommentAddKey = '0415407ff89d57c67141fda6b01ce2c88be6582a12b9ed9d7e213313b7f0a8c55027fdee5ad8ecaafdae357704e1bff9068f7bb78aa233107170c13e77b24ff247'  // 评论提交公钥

    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        fileUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/file/preview/',
        info: { "id": "929924391108743168", "title": "征集主题投票形式单选", "opinionType": 1, "pubOffice": "1769568285222793217", "opinionRange": "all_user", "pubTime": 1762489332711, "content": null, "startTime": 1762444800000, "endTime": 1762531200000, "opinionLayout": { "value": "2", "label": "投票形式", "name": "投票形式", "dictCode": "opinion_layout" }, "outLink": null, "attachmentIds": "", "opinionContent": "<p>征集函内容征集函内容征集函内容征集函内容征集函内容征集函内容征集函内容征集函内容&nbsp;</p>", "checkedStatus": 2, "isDraft": 0, "isTop": 0, "isShare": 0, "contentFileIds": "", "isSplitClause": 0, "isPublicVote": 1, "createDate": 1762489332663, "createBy": "1", "region": "371500", "updateDate": 1762489332663, "delFlag": 0, "businessCode": "legis_contact_point", "businessId": null, "legisOpinionType": { "value": "1", "label": "立法规划", "name": "立法规划", "dictCode": "legis_opinion_type" }, "currentMessageTemplate": null, "needTextMessageNotice": 0, "isCrossOffice": 0, "userRange": "all_user", "userIds": [], "hasUserRange": 1, "officeRange": null, "officeIds": null, "hasOfficeRange": 0, "voteTopic": { "id": "929924391121326080", "topic": "这是投票主题", "businessType": "legis_contact_point", "businessId": "929924391108743168", "maxVote": 1, "startTime": 1762444800000, "endTime": 1762531200000, "noticeMinute": null, "isAnonymous": 1, "topicImg": null, "createBy": "1", "createDate": 1762489332667 }, "hasVote": false, "officeName": "", "voteTopicOptions": [{ "id": "929924391133908992", "topicId": "929924391121326080", "optionContent": "A", "sort": 1, "count": 0, "point": null, "vote": false }, { "id": "929924391150686208", "topicId": "929924391121326080", "optionContent": "B", "sort": 2, "count": 0, "point": null, "vote": false }, { "id": "929924391154880512", "topicId": "929924391121326080", "optionContent": "C", "sort": 3, "count": 0, "point": null, "vote": false }], "contentFileInfos": null, "enlistStatus": "征集中", "opinionSourceType": 2, "opinionSourceCode": "legis_user", "feedback": { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "929924710077173760", "businessId": "929924391108743168", "feedbackDate": 1762444800000, "content": "<p><span style=\"font-family: 微软雅黑; font-size: 12pt;\">投票形式的立法征询在来反馈一下</span></p>", "pictures": null, "attachmentIds": "50e2883a-0133-4bfa-bde2-a47c636f6220", "createBy": "1", "createDate": 1762489408711, "updateDate": 1762489408711, "moduleId": null, "businessCode": "legis_contact_point", "createName": "Admin", "attachments": [{ "id": "50e2883a-0133-4bfa-bde2-a47c636f6220", "originalFileName": "2.4.4西安市“数字政协”平台压力测试报告V1.0.pdf", "extName": "pdf", "newFileName": "50e2883a-0133-4bfa-bde2-a47c636f6220.pdf", "fileSize": 963355, "isInitFile": 0, "isKeepAlive": 0, "createBy": "1", "createByName": "Admin", "createDate": 1762489407968, "tracy": null }] }, "crosRegionItems": null, "attachments": [] },
        commentList: [],
        // 评论相关数据
        showPopup: false,
        commentText: '',
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          // this.getInfo()
          // this.getCommentList()
        }
      },
      methods: {
        // 获取立法征询详情
        getInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'legislationOpinionInfo'
          var interfacecontent = { detailId: id, isPublic: true, userId: JSON.parse(localStorage.getItem('gphone')) }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicLegislativeInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.info = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInfo()
            }
          })
        },

        // 获取评论列表
        getCommentList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'findOpionComment'
          var interfacecontent = {
            checkedStatus: '',
            pageNo: 1,
            pageSize: 99,
            query: { businessCode: 'legis_contact_point', businessId: id, extRed: that.info.opinionSourceCode || '', extYellow: '' }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicCommentListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.commentList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getCommentList()
            }
          })
        },

        // 附件预览
        clickfile (attachment) {
          console.log('文件信息:', this.fileUrl + attachment.id);
          window.location.href = 'https://cszysoft.com/appShare/previews/index.html?url=' + this.fileUrl + attachment.id
        },

        // 打开外部链接
        openOutLink () {
          if (!this.info.outLink) {
            return
          }
          window.location.href = this.data.info.outLink
        },

        // 显示评论弹窗
        showCommentPopup () {
          this.showPopup = true
          this.commentText = ''
          // 延迟聚焦到文本框
          this.$nextTick(() => {
            const textarea = document.querySelector('.comment_textarea')
            if (textarea) {
              textarea.focus()
            }
          })
        },

        // 隐藏评论弹窗
        hideCommentPopup () {
          this.showPopup = false
          this.commentText = ''
        },

        // 提交评论
        submitComment () {
          if (!this.commentText.trim()) {
            vant.Toast('请输入评论内容')
            return
          }
          // 显示加载中提示
          vant.Toast.loading({
            mask: true,
            message: '提交中...'
          })
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'commentAdds'
          var interfacecontent = {
            form: {
              businessCode: 'legis_contact_point',
              businessId: id,
              commentContent: this.commentText,
              extBlue: 2,
              extGreen: id,
              extRed: this.info.opinionSourceCode || '',
              extYellow: id,
              jordan: 'contact_point',
              terminalName: 'PUBLIC',
              commentUserName: JSON.parse(localStorage.getItem('gname'))
            }
          }
          let extraData = {
            header: {
              'u-login-areaId': '371500',
              'u-terminal': 'PUBLIC'
            }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicCommentAddKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              if (ret.code == 200) {
                vant.Toast.success('评论发表成功！');
                setTimeout(() => {
                  this.hideCommentPopup()
                }, 1000)
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('提交留言结果发生错误：', error)
            }
          })
        },

        // 

        // 投票
        submitVote () {

        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>