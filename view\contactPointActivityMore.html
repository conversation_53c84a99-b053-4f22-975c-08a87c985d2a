<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>联系点活动列表</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      height: 100vh;
    }

    .search_box {
      background: #fff
    }

    .contactPointActivityList {
      margin: 10px 16px;
    }

    .contactPointActivityItem {
      background: #FFFFFF;
      box-shadow: 0px 2px 10px 0px rgba(24, 64, 118, 0.08);
      padding: 10px;
      margin-bottom: 10px;
      position: relative;
    }

    .status-tag {
      background: rgba(246, 147, 28, 0.08);
      padding: 4px 10px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
    }

    .status-tag-no {
      background: rgba(102, 102, 102, 0.1);
      padding: 4px 10px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: rgb(102, 102, 102);
    }

    .contactPointActivityContent {
      margin-top: 10px;
    }

    .contactPointActivityTitle {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 10px;
      line-height: 24px;
    }

    .contactPointActivityTimeBox {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .clock-icon {
      width: 14px;
      height: 14px;
      margin-right: 6px;
    }

    .contactPointActivityTimeRemaining {
      font-weight: 600;
      font-size: 28rpx;
      color: #333333;
    }

    .time-highlight {
      color: #FF3B30;
      font-weight: 500;
    }

    .contactPointActivityLocation,
    .contactPointActivityTime {
      margin-bottom: 5px;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
    }

    /* 查看详情按钮样式 */
    .contactPointActivityBtn {
      font-size: 16px;
      color: #333333;
      text-align: center;
      line-height: 30px;
      margin-top: 15px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <van-tabs v-model="active" shrink color="#0D75FF" swipeable @click="onClickTab">
      <van-tab v-for="(item,index) in activeData" :key="item.id" :name="item.id" :title="item.value">
        <div class="search_box">
          <van-search v-model="searchKeyword" placeholder="请输入关键词" @search="onSearch" />
        </div>
        <van-list v-model:loading="loading" :finished="finished" :immediate-check="false" finished-text="没有更多了"
          offset="52" @load="onLoad">
          <div class="contactPointActivityList" v-cloak>
            <div class="contactPointActivityItem" v-for="item in dataList" :key="item.id" @click="openDetails(item)">
              <div class="flex_box flex_align_center">
                <div :class="item.status=='进行中'||item.status=='未开始'?'status-tag':'status-tag-no'">{{item.status}}</div>
                <div></div>
              </div>
              <div class="contactPointActivityContent">
                <div class="contactPointActivityTitle">{{item.title}}</div>
                <div class="contactPointActivityTimeBox" v-if="item.status === '进行中' && item.timeRemaining">
                  <image src="../img/icon_time1.png" class="clock-icon" mode="aspectFit"></image>
                  <span class="contactPointActivityTimeRemaining">距离活动结束剩余：<span
                      class="time-highlight">{{item.timeRemaining.days}}天{{item.timeRemaining.hours}}小时{{item.timeRemaining.minutes}}分钟</span></span>
                </div>
                <div class="contactPointActivityLocation">活动地点：{{item.publishLocation||'暂无'}} </div>
                <div class="contactPointActivityTime">活动时间：{{formatTime(item.startDate)}} 至
                  {{formatTime(item.endDate)}}
                </div>
                <div class="contactPointActivityBtn" data-id="{{item.id}}">查看详情</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicReleaseListKey = '0498561f9e1d00d373fb262dcddb5040276d2d335677431330ebfe60314857aa3a2ab886363243dd4a994e0276b1f456e229f16d56f67567a40890172dad20f855' // 联系点活动列表公钥

    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        active: '0',
        activeData: [
          { id: '0', value: '全部' },
          { id: 'under_way', value: '进行中' },
          { id: 'not_yet_started', value: '未开始' },
          { id: 'already_ended', value: '已结束' }
        ],
        searchKeyword: '',
        loading: false,
        finished: false,
        pageNo: 1,
        pageSize: 15,
        flag: true,
        dataList: []
      },
      async mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          this.getList()
        }
      },
      methods: {
        // 获取联系点活动列表
        getList () {
          var that = this
          that.flag = false
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawConsultRegulationList'
          var interfacecontent = {
            keyword: this.searchKeyword,
            pageNo: 1,
            pageSize: 99,
            tableId: 'law_consult_regulation',
            status: this.active || null,
            query: { consultRegulationStatus: 2, lawPointId: id, moduleType: '2' }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicReleaseListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取联系点活动列表', ret)
              var { total } = ret
              if (that.pageNo === 1) {
                that.dataList = ret.data || []
              } else {
                that.dataList = that.dataList.concat(ret.data)
              }
              that.pageNo = that.pageNo + 1
              that.loading = false
              that.flag = true
              // 数据全部加载完成
              if (that.dataList.length >= total) {
                that.finished = true
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getList()
            }
          })
        },
        // 下拉加载
        onLoad () {
          if (this.flag) {
            this.getList()
          }
        },
        // 搜索
        onSearch (val) {
          this.searchKeyword = val
          this.dataList = []
          this.pageNo = 1
          this.finished = false
          this.flag = false
          this.getList()
        },
        // 打开联系点活动详情
        openDetails (item) {
          window.location.href = './contactPointActivityDetails.html?id=' + item.id
        },
        // tab切换
        onClickTab (val) {
          this.active = val
          this.dataList = []
          this.pageNo = 1
          this.finished = false
          this.flag = false
          this.getList()
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>