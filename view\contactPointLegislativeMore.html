<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>立法征询列表</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      height: 100vh;
    }

    contactPointLegislativeMore .search_box {
      background: #fff
    }

    .contactPointLegislativeList {
      margin: 10px 16px;
    }

    .contactPointLegislativeItem {
      background: #FFFFFF;
      box-shadow: 0px 2px 10px 0px rgba(24, 64, 118, 0.08);
      padding: 10px;
      margin-bottom: 10px;
      position: relative;
    }

    .legislative_status {
      padding: 3px 10px;
      background-color: #FFF5E6;
      color: #E67E22;
      font-size: 14px;
      border-radius: 3px;
    }

    .legislative_type {
      color: #165DFF;
      font-size: 14px;
    }

    .legislative_content {
      margin-top: 10px;
    }

    .legislative_title {
      font-size: 18px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 10px;
      line-height: 24px;
    }

    .legislative_time {
      margin-bottom: 5px;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <van-tabs v-model="active" shrink color="#0D75FF" swipeable @click="onClickTab">
      <van-tab v-for="(item,index) in activeData" :key="item.id" :name="item.id" :title="item.value">
        <div class="search_box">
          <van-search v-model="searchKeyword" placeholder="请输入关键词" @search="onSearch" />
        </div>
        <van-list v-model:loading="loading" :finished="finished" :immediate-check="false" finished-text="没有更多了"
          offset="52" @load="onLoad">
          <div class="contactPointLegislativeList" v-cloak>
            <div class="contactPointLegislativeItem" v-for="item in dataList" :key="item.id" @click="openDetails(item)">
              <div class="flex_box flex_align_center flex_justify_between">
                <div class="legislative_status">{{item.joinState}}</div>
                <div class="legislative_type">{{item.opinionSource}}</div>
              </div>
              <div class="legislative_content">
                <div class="legislative_title">{{item.title}}</div>
                <div class="legislative_time">征集日期：{{formatTime(item.startTime)}} 至 {{formatTime(item.endTime)}}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicOpinionListKey = '04a24ffac5944585d915cb23e1a6cac255d2586d66eff6e01d4d5b2ffef94bcd365131d86f6b1c8c2c153508e7aa86410f0f7290102d45a172c383442702a3470a' // 获取立法征询公钥

    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        active: '',
        activeData: [
          { id: '', value: '全部' },
          { id: '5', value: '进行中' },
          { id: '0', value: '未开始' },
          { id: '6', value: '已结束' }
        ],
        searchKeyword: '',
        loading: false,
        finished: false,
        pageNo: 1,
        pageSize: 15,
        flag: true,
        dataList: []
      },
      async mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          this.getList()
        }
      },
      methods: {
        // 获取立法征询列表
        getList () {
          var that = this
          that.flag = false
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'legislationOpinionList'
          var interfacecontent = {
            areaId: '371500',
            isManage: false,
            keyword: that.searchKeyword,
            opinionType: this.active,
            pageNo: 1,
            pageSize: 99,
            positionId: id,
            query: { businessCode: 'legis_contact_point', isDraft: 0 }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicOpinionListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取立法征询列表', ret)
              var { total } = ret
              ret.data.forEach(item => {
                item.joinState = item.hasPartIn == "0" ? "未参与" : item.hasPartIn == "1" ? "已参与" : ""
              })
              if (that.pageNo === 1) {
                that.dataList = ret.data || []
              } else {
                that.dataList = that.dataList.concat(ret.data)
              }
              that.pageNo = that.pageNo + 1
              that.loading = false
              that.flag = true
              // 数据全部加载完成
              if (that.dataList.length >= total) {
                that.finished = true
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getList()
            }
          })
        },
        // 下拉加载
        onLoad () {
          if (this.flag) {
            this.getList()
          }
        },
        // 搜索
        onSearch (val) {
          this.searchKeyword = val
          this.dataList = []
          this.pageNo = 1
          this.finished = false
          this.flag = false
          this.getList()
        },
        // 打开联系点活动详情
        openDetails (item) {
          window.location.href = './contactPointLegislativeDetails.html?id=' + item.id
        },
        // tab切换
        onClickTab (val) {
          this.active = val
          this.dataList = []
          this.pageNo = 1
          this.finished = false
          this.flag = false
          this.getList()
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>